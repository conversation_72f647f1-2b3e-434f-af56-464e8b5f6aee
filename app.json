{"expo": {"name": "ark", "slug": "ark", "version": "1.0.0", "orientation": "portrait", "icon": "./src/assets/images/icon.png", "scheme": "ark", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "bundleIdentifier": "com.x10xcrazyhorse.ark"}, "android": {"adaptiveIcon": {"foregroundImage": "./src/assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "package": "com.x10xcrazyhorse.ark"}, "web": {"bundler": "metro", "output": "static", "favicon": "./src/assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./src/assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}], "expo-font", "expo-secure-store"], "experiments": {"typedRoutes": true}}}
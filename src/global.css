@tailwind base;
@tailwind components;
@tailwind utilities;

/* Design System CSS Variables */
:root {
  /* Colors */
  --tuebor-black: #0f0f0f;
  --tuebor-black-alt: #141416;
  --tuebor-white: #f9ffff;
  
  /* Orange Palette */
  --orange-300: #fc9c69;
  --orange-400: #fb7c37;
  --orange-500: #fa5b06;
  --orange-600: #c84904;
  --orange-700: #963603;
  
  /* Zinc Scale */
  --zinc-950: #09090b;
  --zinc-900: #18181b;
  --zinc-800: #27272a;
  --zinc-500: #71717a;
  --zinc-400: #a1a1aa;
  --zinc-200: #e4e4e7;
  --zinc-50: #fafafa;
  
  /* Border Radius */
  --radius: 0.5rem;
}

/* Base Styles */
* {
  border-color: var(--zinc-800);
}

body {
  background-color: var(--tuebor-black);
  color: var(--tuebor-white);
  font-family: 'Inter', system-ui, sans-serif;
}

/* Component Utilities */
@layer components {
  .btn-primary {
    @apply bg-orange-500 hover:bg-orange-600 active:bg-orange-700 text-white font-semibold px-6 py-3 rounded-lg transition-colors duration-200;
  }
  
  .btn-secondary {
    @apply bg-transparent border border-zinc-400 hover:border-orange-500 text-zinc-400 hover:text-orange-500 font-semibold px-6 py-3 rounded-lg transition-colors duration-200;
  }
  
  .card {
    @apply bg-zinc-900 border border-zinc-800 rounded-xl p-6;
  }
  
  .text-gradient {
    @apply bg-gradient-to-r from-orange-400 to-orange-600 bg-clip-text text-transparent;
  }
  
  .section-padding {
    @apply px-4 sm:px-6 lg:px-8 py-16 lg:py-24;
  }
  
  .container-max {
    @apply max-w-7xl mx-auto;
  }
}
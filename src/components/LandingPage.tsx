import { LinearGradient } from 'expo-linear-gradient';
import { StatusBar } from 'expo-status-bar';
import React, { useState } from 'react';
import {
    SafeAreaView,
    ScrollView,
    Text,
    TextInput,
    TouchableOpacity,
    View,
} from 'react-native';

const LandingPage = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [openFaq, setOpenFaq] = useState(null);

  const faqs = [
    {
      question: "What is Tuebor Vault?",
      answer: "Tuebor Vault is a comprehensive platform for securing and tracking firearms and documents on the blockchain."
    },
    {
      question: "How do I get access to Tuebor Vault?",
      answer: "You can get access by signing up for one of our plans and completing the verification process."
    },
    {
      question: "Is Tuebor Vault Secure?",
      answer: "Yes, Tuebor Vault uses advanced blockchain technology and encryption to ensure maximum security."
    },
    {
      question: "What if I happen to be suspicious?",
      answer: "Our platform includes comprehensive monitoring and alert systems to handle any suspicious activities."
    },
    {
      question: "What is Tuebor Vault?",
      answer: "Tuebor Vault provides secure, decentralized storage and tracking for your valuable assets and documents."
    }
  ];

  const ChevronDown = ({ isOpen }) => (
    <Text className={`text-zinc-400 text-lg transform ${isOpen ? 'rotate-180' : 'rotate-0'}`}>
      ⌄
    </Text>
  );

  const MenuIcon = () => (
    <View className="w-6 h-6 justify-center items-center">
      <View className="w-5 h-0.5 bg-tuebor-white mb-1" />
      <View className="w-5 h-0.5 bg-tuebor-white mb-1" />
      <View className="w-5 h-0.5 bg-tuebor-white" />
    </View>
  );

  const CloseIcon = () => (
    <View className="w-6 h-6 justify-center items-center">
      <Text className="text-tuebor-white text-xl">✕</Text>
    </View>
  );

  return (
    <SafeAreaView className="flex-1 bg-tuebor-black">
      <StatusBar style="light" />
      <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
        {/* Navigation */}
        <View className="border-b border-zinc-800">
          <View className="section-padding py-4">
            <View className="flex-row justify-between items-center">
              <View className="flex-row items-center space-x-2">
                <View className="w-8 h-8 bg-orange-500 rounded flex items-center justify-center">
                  <Text className="text-tuebor-white font-bold text-sm">A</Text>
                </View>
                <Text className="font-bold text-xl text-tuebor-white">ARK</Text>
              </View>
              
              <TouchableOpacity 
                className="md:hidden"
                onPress={() => setIsMenuOpen(!isMenuOpen)}
              >
                {isMenuOpen ? <CloseIcon /> : <MenuIcon />}
              </TouchableOpacity>
              
              <TouchableOpacity className="btn-primary hidden md:flex">
                <Text className="text-tuebor-white font-semibold">Get Started</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <View className="bg-tuebor-black-alt border-b border-zinc-800">
            <View className="px-4 py-4 space-y-3">
              <TouchableOpacity className="py-2">
                <Text className="text-zinc-400">Home</Text>
              </TouchableOpacity>
              <TouchableOpacity className="py-2">
                <Text className="text-zinc-400">The Vault</Text>
              </TouchableOpacity>
              <TouchableOpacity className="py-2">
                <Text className="text-zinc-400">Features</Text>
              </TouchableOpacity>
              <TouchableOpacity className="py-2">
                <Text className="text-zinc-400">Pricing</Text>
              </TouchableOpacity>
              <TouchableOpacity className="btn-primary mt-4">
                <Text className="text-tuebor-white font-semibold text-center">Get Started</Text>
              </TouchableOpacity>
            </View>
          </View>
        )}

        {/* Hero Section */}
        <LinearGradient
          colors={['#0f0f0f', '#fa5b06']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          className="relative overflow-hidden"
        >
          <View className="section-padding">
            <View className="items-center">
              <Text className="text-orange-400 font-medium mb-4 text-base">
                Blockchain-powered Vault System
              </Text>
              <Text className="text-4xl font-bold mb-6 text-center text-tuebor-white">
                Secure your{' '}
                <Text className="text-orange-500">firearms</Text> and{'\n'}
                <Text className="text-orange-500">documents</Text>, on-chain
              </Text>
              <Text className="text-xl text-zinc-400 mb-8 text-center container-max">
                Track and monitor every asset digitally
              </Text>
              <TouchableOpacity className="btn-primary">
                <Text className="text-tuebor-white font-semibold text-lg">Explore</Text>
              </TouchableOpacity>
            </View>
            
            {/* Dashboard Preview */}
            <View className="mt-16">
              <View className="card bg-zinc-900/50 backdrop-blur">
                {/* Browser Chrome */}
                <View className="flex-row items-center space-x-3 mb-4">
                  <View className="w-3 h-3 bg-red-500 rounded-full" />
                  <View className="w-3 h-3 bg-yellow-500 rounded-full" />
                  <View className="w-3 h-3 bg-green-500 rounded-full" />
                  <View className="flex-1 bg-zinc-800 rounded px-3 py-1 ml-4">
                    <Text className="text-sm text-zinc-400">https://vault.tuebor.com</Text>
                  </View>
                </View>
                
                {/* Dashboard Content */}
                <View className="space-y-4">
                  <View className="bg-zinc-800/50 rounded-lg p-4">
                    <Text className="text-tuebor-white font-medium mb-2">Vault Status</Text>
                    <View className="flex-row justify-around">
                      <View className="items-center">
                        <Text className="text-2xl font-bold text-orange-500">$2.4k</Text>
                        <Text className="text-sm text-zinc-400">Total Value</Text>
                      </View>
                      <View className="items-center">
                        <Text className="text-2xl font-bold text-green-500">+18%</Text>
                        <Text className="text-sm text-zinc-400">This Month</Text>
                      </View>
                    </View>
                  </View>
                  
                  <View className="bg-zinc-800/50 rounded-lg p-4">
                    <Text className="text-tuebor-white font-medium mb-2">Vault Preview</Text>
                    <View className="h-32 bg-zinc-700 rounded-lg items-center justify-center">
                      <Text className="text-zinc-500 text-4xl">📄</Text>
                    </View>
                  </View>
                </View>
              </View>
            </View>
          </View>
        </LinearGradient>

        {/* Product Overview */}
        <View className="section-padding bg-tuebor-black">
          <View className="items-center mb-16">
            <Text className="text-4xl font-bold mb-4 text-center text-tuebor-white">
              Discover <Text className="text-orange-500">ARK</Text> by Tuebor
            </Text>
            <Text className="text-xl text-zinc-400 text-center container-max">
              Secure your most valuable assets with our blockchain-powered vault system designed for modern security needs
            </Text>
          </View>
          
          <View className="space-y-12">
            <View>
              <Text className="text-2xl font-bold mb-6 text-orange-500">SECURITY</Text>
              <Text className="text-zinc-400 mb-8 text-base leading-6">
                Advanced blockchain technology ensures your firearms and documents are secured with military-grade encryption and immutable tracking.
              </Text>
              
              <Text className="text-2xl font-bold mb-6 text-orange-500">PRIVACY</Text>
              <Text className="text-zinc-400 mb-8 text-base leading-6">
                Your data remains completely private and under your control. Zero-knowledge architecture means only you have access to your vault contents.
              </Text>
              
              <Text className="text-2xl font-bold mb-6 text-orange-500">OPTIMIZATION</Text>
              <Text className="text-zinc-400 text-base leading-6">
                Streamlined interface and smart contracts optimize your asset management experience while maintaining the highest security standards.
              </Text>
            </View>
            
            <View className="mt-12">
              <LinearGradient
                colors={['rgba(250, 91, 6, 0.2)', '#18181b']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
                className="rounded-xl p-8 border border-orange-500/20"
              >
                <View className="w-16 h-16 bg-orange-500 rounded-xl items-center justify-center mb-6">
                  <Text className="text-tuebor-white text-2xl">🛡</Text>
                </View>
                <Text className="text-2xl font-bold mb-4 text-tuebor-white">ARK</Text>
                <Text className="text-zinc-400 text-base leading-6">
                  The ultimate vault system for securing and tracking your most valuable possessions with blockchain technology.
                </Text>
              </LinearGradient>
            </View>
          </View>
        </View>

        {/* Features Section */}
        <View className="section-padding bg-zinc-900/50">
          <View className="items-center mb-16">
            <Text className="text-4xl font-bold mb-4 text-center text-tuebor-white">
              Comprehensive <Text className="text-orange-500">Tracking System</Text>
            </Text>
            <Text className="text-xl text-zinc-400 text-center">
              Real-time tracking and monitoring of your assets with advanced analytics
            </Text>
          </View>
          
          <View className="space-y-6">
            {/* Main Features */}
            <View className="card items-center">
              <View className="w-16 h-16 bg-orange-500/20 rounded-full items-center justify-center mb-4">
                <Text className="text-orange-500 text-2xl">🔍</Text>
              </View>
              <Text className="text-xl font-bold mb-3 text-orange-500">TRACKING</Text>
              <Text className="text-zinc-400 text-center text-base leading-6">
                Monitor your assets in real-time with precise location and status tracking powered by blockchain technology.
              </Text>
            </View>
            
            <View className="card items-center">
              <View className="w-16 h-16 bg-orange-500/20 rounded-full items-center justify-center mb-4">
                <Text className="text-orange-500 text-2xl">📊</Text>
              </View>
              <Text className="text-xl font-bold mb-3 text-orange-500">RECORD HISTORY</Text>
              <Text className="text-zinc-400 text-center text-base leading-6">
                Complete audit trail and transaction history for all your assets with immutable blockchain records.
              </Text>
            </View>
            
            <View className="card items-center">
              <View className="w-16 h-16 bg-orange-500/20 rounded-full items-center justify-center mb-4">
                <Text className="text-orange-500 text-2xl">🛡</Text>
              </View>
              <Text className="text-xl font-bold mb-3 text-orange-500">ASSURANCE</Text>
              <Text className="text-zinc-400 text-center text-base leading-6">
                Military-grade security and insurance coverage for your most valuable possessions and documents.
              </Text>
            </View>
          </View>
          
          {/* Additional Features */}
          <View className="mt-12 space-y-6">
            <View className="card">
              <Text className="text-orange-500 text-2xl mb-4">⏰</Text>
              <Text className="text-lg font-bold mb-2 text-tuebor-white">Advanced Scanning</Text>
              <Text className="text-zinc-400 text-sm leading-5">
                Real-time vulnerability scanning and threat detection for all stored assets and documents.
              </Text>
            </View>
            
            <View className="card">
              <Text className="text-orange-500 text-2xl mb-4">📍</Text>
              <Text className="text-lg font-bold mb-2 text-tuebor-white">Monitoring Live</Text>
              <Text className="text-zinc-400 text-sm leading-5">
                24/7 monitoring with instant alerts and notifications for any unauthorized access attempts.
              </Text>
            </View>
            
            <View className="card">
              <Text className="text-orange-500 text-2xl mb-4">📄</Text>
              <Text className="text-lg font-bold mb-2 text-tuebor-white">Compliance</Text>
              <Text className="text-zinc-400 text-sm leading-5">
                Automated compliance reporting and regulatory adherence for all tracked assets and documentation.
              </Text>
            </View>
          </View>
        </View>

        {/* Pricing Section */}
        <View className="section-padding bg-tuebor-black">
          <View className="items-center mb-16">
            <Text className="text-4xl font-bold mb-4 text-tuebor-white text-center">
              Choose Your Plan
            </Text>
            <Text className="text-xl text-zinc-400 text-center">
              Select the perfect plan for your security needs
            </Text>
          </View>
          
          <View className="space-y-8">
            {/* Free Plan */}
            <View className="card">
              <Text className="text-2xl font-bold mb-2 text-tuebor-white">Free</Text>
              <Text className="text-zinc-400 mb-6">Perfect for getting started</Text>
              <Text className="text-4xl font-bold mb-6 text-tuebor-white">$0</Text>
              
              <View className="space-y-3 mb-8">
                <View className="flex-row items-center">
                  <Text className="text-green-500 mr-3 text-lg">✓</Text>
                  <Text className="text-zinc-400">Basic vault access</Text>
                </View>
                <View className="flex-row items-center">
                  <Text className="text-green-500 mr-3 text-lg">✓</Text>
                  <Text className="text-zinc-400">Up to 5 items</Text>
                </View>
                <View className="flex-row items-center">
                  <Text className="text-green-500 mr-3 text-lg">✓</Text>
                  <Text className="text-zinc-400">Basic tracking</Text>
                </View>
                <View className="flex-row items-center">
                  <Text className="text-green-500 mr-3 text-lg">✓</Text>
                  <Text className="text-zinc-400">Email support</Text>
                </View>
              </View>
              
              <TouchableOpacity className="btn-secondary">
                <Text className="text-zinc-400 font-semibold text-center">Current Plan</Text>
              </TouchableOpacity>
            </View>
            
            {/* Premium Plan */}
            <View className="relative">
              <LinearGradient
                colors={['rgba(250, 91, 6, 0.1)', '#18181b']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
                className="rounded-xl p-8 border border-orange-500/30"
              >
                <View className="absolute top-4 right-4 bg-orange-500 px-3 py-1 rounded-full">
                  <Text className="text-tuebor-white text-sm font-medium">Recommended</Text>
                </View>
                <Text className="text-2xl font-bold mb-2 text-tuebor-white">Premium</Text>
                <Text className="text-zinc-400 mb-6">Advanced security features</Text>
                <View className="flex-row items-baseline mb-6">
                  <Text className="text-4xl font-bold text-tuebor-white">$9.99</Text>
                  <Text className="text-lg text-zinc-400 ml-2">/month</Text>
                </View>
                
                <View className="space-y-3 mb-8">
                  <View className="flex-row items-center">
                    <Text className="text-orange-500 mr-3 text-lg">✓</Text>
                    <Text className="text-tuebor-white">Unlimited vault access</Text>
                  </View>
                  <View className="flex-row items-center">
                    <Text className="text-orange-500 mr-3 text-lg">✓</Text>
                    <Text className="text-tuebor-white">Unlimited items</Text>
                  </View>
                  <View className="flex-row items-center">
                    <Text className="text-orange-500 mr-3 text-lg">✓</Text>
                    <Text className="text-tuebor-white">Advanced tracking & analytics</Text>
                  </View>
                  <View className="flex-row items-center">
                    <Text className="text-orange-500 mr-3 text-lg">✓</Text>
                    <Text className="text-tuebor-white">Priority support</Text>
                  </View>
                  <View className="flex-row items-center">
                    <Text className="text-orange-500 mr-3 text-lg">✓</Text>
                    <Text className="text-tuebor-white">Insurance coverage</Text>
                  </View>
                  <View className="flex-row items-center">
                    <Text className="text-orange-500 mr-3 text-lg">✓</Text>
                    <Text className="text-tuebor-white">Advanced notifications</Text>
                  </View>
                </View>
                
                <TouchableOpacity className="btn-primary">
                  <Text className="text-tuebor-white font-semibold text-center">Upgrade to Premium</Text>
                </TouchableOpacity>
              </LinearGradient>
            </View>
          </View>
        </View>

        {/* CTA Section */}
        <LinearGradient
          colors={['rgba(250, 91, 6, 0.1)', '#18181b']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          className="section-padding"
        >
          <View className="items-center">
            <Text className="text-4xl font-bold mb-6 text-tuebor-white text-center">
              Track what you own. Know what you have.
            </Text>
            <Text className="text-xl text-zinc-400 mb-8 text-center container-max">
              Start securing your firearms and documents today with our advanced blockchain vault system.
            </Text>
            <TouchableOpacity className="btn-primary">
              <Text className="text-tuebor-white font-semibold text-lg">Get Started Today</Text>
            </TouchableOpacity>
          </View>
        </LinearGradient>

        {/* FAQ Section */}
        <View className="section-padding bg-tuebor-black">
          <View className="items-center mb-16">
            <Text className="text-4xl font-bold mb-4 text-center">
              <Text className="text-orange-500">Frequently Asked</Text>{'\n'}
              <Text className="text-tuebor-white">Questions</Text>
            </Text>
            <Text className="text-zinc-400 text-center">
              Find answers to common questions about our vault system
            </Text>
          </View>
          
          <View className="space-y-4">
            {faqs.map((faq, index) => (
              <View key={index} className="card">
                <TouchableOpacity
                  className="flex-row items-center justify-between"
                  onPress={() => setOpenFaq(openFaq === index ? null : index)}
                >
                  <Text className="font-medium text-tuebor-white flex-1 pr-4">{faq.question}</Text>
                  <ChevronDown isOpen={openFaq === index} />
                </TouchableOpacity>
                {openFaq === index && (
                  <View className="mt-4 pt-4 border-t border-zinc-800">
                    <Text className="text-zinc-400 leading-6">{faq.answer}</Text>
                  </View>
                )}
              </View>
            ))}
          </View>
        </View>

        {/* Footer */}
        <View className="border-t border-zinc-800 section-padding bg-tuebor-black">
          <View className="space-y-8">
            <View>
              <View className="flex-row items-center space-x-2 mb-4">
                <View className="w-8 h-8 bg-orange-500 rounded items-center justify-center">
                  <Text className="text-tuebor-white font-bold text-sm">A</Text>
                </View>
                <Text className="font-bold text-xl text-tuebor-white">ARK</Text>
              </View>
              <Text className="text-zinc-400 text-sm leading-5">
                Secure blockchain vault system for modern asset protection and tracking.
              </Text>
            </View>
            
            <View className="space-y-6">
              <View>
                <Text className="font-semibold mb-4 text-tuebor-white">Product</Text>
                <View className="space-y-2">
                  <TouchableOpacity><Text className="text-zinc-400 text-sm">Features</Text></TouchableOpacity>
                  <TouchableOpacity><Text className="text-zinc-400 text-sm">Security</Text></TouchableOpacity>
                  <TouchableOpacity><Text className="text-zinc-400 text-sm">Pricing</Text></TouchableOpacity>
                  <TouchableOpacity><Text className="text-zinc-400 text-sm">Enterprise</Text></TouchableOpacity>
                </View>
              </View>
              
              <View>
                <Text className="font-semibold mb-4 text-tuebor-white">Company</Text>
                <View className="space-y-2">
                  <TouchableOpacity><Text className="text-zinc-400 text-sm">About</Text></TouchableOpacity>
                  <TouchableOpacity><Text className="text-zinc-400 text-sm">Blog</Text></TouchableOpacity>
                  <TouchableOpacity><Text className="text-zinc-400 text-sm">Careers</Text></TouchableOpacity>
                  <TouchableOpacity><Text className="text-zinc-400 text-sm">Contact</Text></TouchableOpacity>
                </View>
              </View>
              
              <View>
                <Text className="font-semibold mb-4 text-tuebor-white">Newsletter</Text>
                <Text className="text-zinc-400 text-sm mb-4 leading-5">
                  Stay updated with the latest security features
                </Text>
                <View className="flex-row">
                  <TextInput 
                    placeholder="Enter your email"
                    placeholderTextColor="#71717a"
                    className="flex-1 bg-zinc-800 border border-zinc-700 rounded-l-lg px-3 py-2 text-sm text-tuebor-white"
                  />
                  <TouchableOpacity className="bg-orange-500 px-4 py-2 rounded-r-lg">
                    <Text className="text-tuebor-white text-sm font-medium">Subscribe</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
            
            <View className="border-t border-zinc-800 pt-8 space-y-4">
              <Text className="text-zinc-400 text-sm text-center">
                © 2024 ARK by Tuebor. All rights reserved.
              </Text>
              <View className="flex-row justify-center space-x-6">
                <TouchableOpacity><Text className="text-zinc-400 text-sm">Privacy Policy</Text></TouchableOpacity>
                <TouchableOpacity><Text className="text-zinc-400 text-sm">Terms of Service</Text></TouchableOpacity>
                <TouchableOpacity><Text className="text-zinc-400 text-sm">Cookie Policy</Text></TouchableOpacity>
              </View>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default LandingPage;
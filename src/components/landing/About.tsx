import { Ionicons } from '@expo/vector-icons';
import React from 'react';
import { Image, Text, View } from 'react-native';

interface StatItemProps {
  value: string;
  label: string;
  icon: keyof typeof Ionicons.glyphMap;
}

const StatItem: React.FC<StatItemProps> = ({ value, label, icon }) => {
  return (
    <View className="items-center p-4">
      <View className="mb-2">
        <Ionicons name={icon} size={28} color="#f97316" />
      </View>
      <Text className="text-2xl font-bold text-white mb-1">{value}</Text>
      <Text className="text-gray-400 text-center">{label}</Text>
    </View>
  );
};

const About = () => {
  const stats: StatItemProps[] = [
    { value: '10M+', label: 'Assets Protected', icon: 'shield-checkmark' },
    { value: '100+', label: 'Enterprise Clients', icon: 'business' },
    { value: '99.99%', label: 'Security Uptime', icon: 'lock-closed' },
    { value: '24/7', label: 'Monitoring', icon: 'eye' },
  ];

  return (
    <View className="py-16 px-4 bg-black">
      <View className="max-w-6xl mx-auto">
        {/* Section Header */}
        <View className="mb-12 items-center">
          <Text className="text-orange-500 font-semibold mb-2 text-center">DISCOVER</Text>
          <Text className="text-4xl font-bold text-white mb-4 text-center">The Ark by Tuebor</Text>
          <Text className="text-xl text-gray-400 max-w-2xl text-center">
            Revolutionary blockchain technology for protecting intellectual property and ensuring digital privacy in the modern age.
          </Text>
        </View>

        {/* Three Column Layout */}
        <View className="flex-col md:flex-row items-center justify-between">
          {/* Left Column - ARK Logo */}
          <View className="md:w-1/3 mb-8 md:mb-0">
            <View className="items-center justify-center">
              <Image
                source={require('@/assets/images/landing/logo.png')}
                className="w-48 h-32"
                resizeMode="contain"
              />
            </View>
          </View>

          {/* Center Column - Tuebor Logo */}
          <View className="md:w-1/3 mb-8 md:mb-0">
            <View className="items-center justify-center">
              <Image
                source={require('@/assets/images/landing/tuebor-logo.png')}
                className="w-48 h-48"
                resizeMode="contain"
              />
            </View>
          </View>

          {/* Right Column - Content */}
          <View className="md:w-1/3">
            <Text className="text-3xl font-bold text-white mb-6">
              Built for the Future of Digital Security
            </Text>
            <Text className="text-gray-400 mb-6 leading-relaxed">
              The Ark represents the next evolution in intellectual property protection, combining cutting-edge blockchain 
              technology with military-grade security to create an impenetrable digital fortress for your most valuable assets.
            </Text>
            <Text className="text-gray-400 mb-6 leading-relaxed">
              From creative works to proprietary algorithms, from personal data to corporate secrets - The Ark ensures 
              your digital legacy remains protected, private, and perpetually yours.
            </Text>
            
            {/* Key Features */}
            <View className="mb-8">
              <Text className="text-xl font-semibold text-white mb-4">Core Capabilities</Text>
              <View className="space-y-3">
                <View className="flex-row items-center">
                  <Ionicons name="checkmark-circle" size={20} color="#f97316" />
                  <Text className="text-gray-300 ml-2 font-medium">Immutable proof of ownership</Text>
                </View>
                <View className="flex-row items-center">
                  <Ionicons name="checkmark-circle" size={20} color="#f97316" />
                  <Text className="text-gray-300 ml-2 font-medium">Zero-knowledge privacy protection</Text>
                </View>
                <View className="flex-row items-center">
                  <Ionicons name="checkmark-circle" size={20} color="#f97316" />
                  <Text className="text-gray-300 ml-2 font-medium">Automated smart contract licensing</Text>
                </View>
                <View className="flex-row items-center">
                  <Ionicons name="checkmark-circle" size={20} color="#f97316" />
                  <Text className="text-gray-300 ml-2 font-medium">Decentralized asset management</Text>
                </View>
              </View>
            </View>
          </View>
        </View>

        {/* Stats Row */}
        <View className="mt-16 bg-gray-900 rounded-xl p-6 border border-gray-800">
          <View className="flex-row flex-wrap justify-around">
            {stats.map((stat, index) => (
              <StatItem key={index} {...stat} />
            ))}
          </View>
        </View>
      </View>
    </View>
  );
};

export default About;
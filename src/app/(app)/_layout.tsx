import { useAuth } from '@clerk/clerk-expo';
import { Redirect, Slot, useSegments } from 'expo-router';

const Layout = () => {
  const segments = useSegments() as string[]; // casting to bypass the strict typed routes inference
  // Check if we're in the authenticated group by looking at the correct segment index
  // segments structure: ['(app)', '(authenticated)', ...] or ['(app)', '(public)', ...]
  const inAuthGroup = segments.includes('(authenticated)');
  const inPublicGroup = segments.includes('(public)');

  const { isSignedIn } = useAuth();

  // If user is not signed in but trying to access authenticated routes
  if (!isSignedIn && inAuthGroup) {
    return <Redirect href="/login" />;
  }

  // If user is signed in but in public routes, redirect to authenticated area
  if (isSignedIn && inPublicGroup) {
    return <Redirect href="/" />;
  }

  return <Slot />;
};

export default Layout;
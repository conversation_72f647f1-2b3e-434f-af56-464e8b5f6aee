ni nativewind tailwindcss@^3.4.17 react-native-reanimated@3.16.2 react-native-safe-area-context
bx tailwind init
bx customize babel.config.js
bx expo customize babel.config.js
bx expo install expo-dev-client @expo-google-fonts/poppins expo-secure-store expo-font expo-image-picker expo-checkbox expo-linear-gradient
ni @clerk/clerk-expo @clerk/backend @sentry/react-native convex @react-navigation/drawer
ni  @clerk/expo-passkeys??
bunx convex dev
bunx @sentry/wizard@latest -i reactNative --saas
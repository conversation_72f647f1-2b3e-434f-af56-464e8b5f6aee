# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Commands

### Development
- `npm start` - Start the Expo development server
- `npm run android` - Run on Android device/emulator
- `npm run ios` - Run on iOS device/simulator
- `npm run web` - Run in web browser

### Code Quality
- `npm run lint` - Run ESLint with Expo configuration

### Utilities
- `npm run reset-project` - Reset project to clean state

## Architecture Overview

This is a React Native mobile application built with Expo SDK 53 and TypeScript. The app uses file-based routing with Expo Router v5.

### Key Technologies
- **Frontend**: React Native 0.79.2 with React 19.0.0
- **Navigation**: Expo Router v5 with file-based routing
- **Styling**: NativeWind v4 (Tailwind CSS for React Native)
- **Authentication**: Clerk with SSO support (Google, Apple, Microsoft) and passkeys
- **Backend**: Convex for real-time data and API
- **Error Tracking**: Sentry with mobile replay

### Project Structure
- `/src/app/` - File-based routing structure
  - `(app)/` - Main app wrapper with auth checks
  - `(authenticated)/` - Protected routes requiring login
  - `(public)/` - Public routes (login, verify, FAQ)
- `/convex/` - Convex backend functions and schema (currently empty)
- `/src/components/` - Reusable components
- `/src/assets/` - Images and fonts

### Path Aliases
- `@/*` maps to `src/*`
- `@/convex/*` maps to `convex/*`
- `~/*` maps to root directory

### Authentication Flow
The app implements a complete authentication flow using Clerk:
1. Email authentication with verification
2. SSO providers (Google, Apple, Microsoft)
3. Passkey support
4. Token caching for performance
5. Auth state redirects between public and authenticated routes

### Styling System
- Uses NativeWind (Tailwind CSS for React Native)
- Global styles in `src/global.css`
- Custom fonts: Poppins (weights: 400, 500, 600, 700)
- Supports light/dark themes

### Current State
The main app functionality (post-authentication) is not yet implemented. The Convex backend is integrated but no functions are defined yet.

The app has a complete landing page and authentication system set up. Next step is to match the design of the landing page to the Figma design in `src/assets/images/landing-page.png`. 
#!/bin/bash

# Fetch Figma code for node 818:364 - add "nodeId": "818:364",
curl -X POST http://127.0.0.1:3845/sse \
  -H "Content-Type: application/json" \
  -d '{
    "method": "tools/call",
    "params": {
      "name": "get_code",
      "arguments": {
        "clientFrameworks": "react-native, expo",
        "clientLanguages": "typescript, javascript",
        "clientModel": "claude-opus-4-20250514",
        "clientName": "claude code"
      }
    }
  }' \
  > figma-responses/figma-code-818-364.json

echo "Response saved to figma-responses/figma-code-818-364.json"
const puppeteer = require('puppeteer');

(async () => {
  const browser = await puppeteer.launch({ headless: false });
  const page = await browser.newPage();
  
  try {
    await page.setViewport({ width: 1200, height: 800 });
    await page.goto('http://localhost:8081/', { 
      waitUntil: 'networkidle2',
      timeout: 10000 
    });
    
    // Wait a bit more for any dynamic content
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    await page.screenshot({ 
      path: 'app-screenshot.png',
      fullPage: true 
    });
    
    console.log('Screenshot saved as app-screenshot.png');
    
    // Get any console errors
    const errors = await page.evaluate(() => {
      return window._expo_static_error || null;
    });
    
    if (errors) {
      console.log('Expo Static Errors:', JSON.stringify(errors, null, 2));
    }
    
  } catch (error) {
    console.error('Error taking screenshot:', error);
  } finally {
    await browser.close();
  }
})();
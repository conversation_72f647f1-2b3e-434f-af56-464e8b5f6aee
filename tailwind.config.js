/** @type {import('tailwindcss').Config} */
module.exports = {
  // NOTE: Update this to include the paths to all of your component files.
  content: ['./src/**/*.{js,jsx,ts,tsx}'],
  presets: [require('nativewind/preset')],
  theme: {
    extend: {
      colors: {
        tuebor: {
          black: '#0f0f0f',
          'black-alt': '#141416',
          white: '#f9ffff',
        },
        orange: {
          300: '#fc9c69',
          400: '#fb7c37', 
          500: '#fa5b06', // Primary orange
          600: '#c84904',
          700: '#963603',
        },
        zinc: {
          950: '#09090b',
          900: '#18181b',
          800: '#27272a',
          500: '#71717a',
          400: '#a1a1aa',
          200: '#e4e4e7',
          50: '#fafafa',
        },
      },
      fontFamily: {
        inter: ['Inter', 'system-ui', 'sans-serif'],
        'sf-pro': ['SF Pro Display', 'system-ui', 'sans-serif'],
      },
      fontSize: {
        '5xl': ['48px', { lineHeight: '48px', fontWeight: '600' }],
        '4xl': ['36px', { lineHeight: '40px', fontWeight: '600' }],
        '2xl': ['24px', { lineHeight: '32px', fontWeight: '600' }],
        'xl': ['20px', { lineHeight: '28px', fontWeight: '600' }],
        'base': ['16px', { lineHeight: '24px' }],
        'sm': ['14px', { lineHeight: '20px' }],
      },
      borderRadius: {
        'lg': '8px',
        'xl': '12px',
      },
      backgroundImage: {
        'gradient-hero': 'linear-gradient(135deg, #0f0f0f 0%, #fa5b06 100%)',
        'gradient-dark': 'linear-gradient(180deg, #09090b 0%, #18181b 100%)',
      },
    },
  },
  plugins: [],
};